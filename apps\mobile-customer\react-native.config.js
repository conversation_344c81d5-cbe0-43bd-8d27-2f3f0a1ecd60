module.exports = {
  project: {
    android: {
      sourceDir: './android',
      appName: 'app',
      packageName: 'com.tap2go.mobile',
    },
  },
  dependencies: {
    // Override expo autolinking to use correct package
    'expo': {
      platforms: {
        android: {
          sourceDir: '../../../node_modules/expo/android',
          packageImportPath: 'import expo.modules.ExpoModulesPackage;',
        },
      },
    },
  },
};