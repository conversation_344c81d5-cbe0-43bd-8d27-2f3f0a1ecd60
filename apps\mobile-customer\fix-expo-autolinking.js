#!/usr/bin/env node

/**
 * Comprehensive Expo Autolinking Fix
 * This script fixes the expo.core.ExpoModulesPackage import issue
 * by creating a custom autolinking configuration and monitoring for changes
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 Starting comprehensive Expo autolinking fix...');

// Check if we're in EAS build environment
const isEASBuild = process.env.EAS_BUILD_WORKINGDIR || process.env.EAS_BUILD;

if (isEASBuild) {
    console.log('✅ EAS Build environment detected');
} else {
    console.log('ℹ️  Local environment detected');
}

// Function to fix PackageList.java import
function fixPackageListImport(packageListFile) {
    if (!fs.existsSync(packageListFile)) {
        return false;
    }
    
    console.log(`🔍 Checking: ${packageListFile}`);
    
    try {
        let content = fs.readFileSync(packageListFile, 'utf8');
        
        if (content.includes('import expo.core.ExpoModulesPackage;')) {
            console.log('🔧 Fixing incorrect import: expo.core.ExpoModulesPackage -> expo.modules.ExpoModulesPackage');
            
            // Create backup
            const backupFile = packageListFile + '.backup';
            fs.writeFileSync(backupFile, content);
            
            // Fix the import statement
            content = content.replace(/import expo\.core\.ExpoModulesPackage;/g, 'import expo.modules.ExpoModulesPackage;');
            
            // Write the fixed content
            fs.writeFileSync(packageListFile, content);
            
            // Verify the fix
            const fixedContent = fs.readFileSync(packageListFile, 'utf8');
            if (fixedContent.includes('import expo.modules.ExpoModulesPackage;')) {
                console.log('✅ Successfully fixed import statement');
                return true;
            } else {
                console.log('❌ Failed to fix import statement, restoring backup');
                fs.writeFileSync(packageListFile, content);
                return false;
            }
        } else {
            console.log('ℹ️  Import already correct or not found');
            return true;
        }
    } catch (error) {
        console.log(`❌ Error processing ${packageListFile}: ${error.message}`);
        return false;
    }
}

// Function to create a custom PackageList.java with correct imports
function createCustomPackageList() {
    const packageListDir = 'android/app/src/main/java/com/facebook/react';
    const packageListPath = path.join(packageListDir, 'PackageList.java');
    
    // Only create if android directory exists
    if (!fs.existsSync('android')) {
        console.log('ℹ️  Android directory not found, skipping custom PackageList creation');
        return false;
    }
    
    const packageListTemplate = `package com.facebook.react;

import android.app.Application;
import android.content.Context;
import android.content.res.Resources;

import com.facebook.react.ReactPackage;
import com.facebook.react.shell.MainPackageConfig;
import com.facebook.react.shell.MainReactPackage;
import java.util.Arrays;
import java.util.List;

// Expo modules - CORRECT IMPORT
import expo.modules.ExpoModulesPackage;

public class PackageList {
  private Application mApplication;
  private ReactNativeHost mReactNativeHost;
  private MainPackageConfig mConfig;

  public PackageList(ReactNativeHost reactNativeHost) {
    this(reactNativeHost, null);
  }

  public PackageList(Application application) {
    this(application, null);
  }

  public PackageList(ReactNativeHost reactNativeHost, MainPackageConfig config) {
    mReactNativeHost = reactNativeHost;
    mConfig = config;
  }

  public PackageList(Application application, MainPackageConfig config) {
    mReactNativeHost = null;
    mApplication = application;
    mConfig = config;
  }

  private ReactNativeHost getReactNativeHost() {
    return mReactNativeHost;
  }

  private Resources getResources() {
    return getApplication().getResources();
  }

  private Application getApplication() {
    if (mApplication == null) mApplication = getReactNativeHost().getApplication();
    return mApplication;
  }

  private Context getApplicationContext() {
    return getApplication().getApplicationContext();
  }

  public List<ReactPackage> getPackages() {
    return Arrays.<ReactPackage>asList(
      new MainReactPackage(mConfig),
      new ExpoModulesPackage()
    );
  }
}`;

    try {
        // Create directory if it doesn't exist
        if (!fs.existsSync(packageListDir)) {
            fs.mkdirSync(packageListDir, { recursive: true });
            console.log(`📁 Created directory: ${packageListDir}`);
        }
        
        // Write the custom PackageList.java
        fs.writeFileSync(packageListPath, packageListTemplate);
        console.log(`✅ Created custom PackageList.java with correct imports`);
        return true;
    } catch (error) {
        console.log(`❌ Error creating custom PackageList.java: ${error.message}`);
        return false;
    }
}

// Main fix function
function runAutolinkingFix() {
    console.log('🚀 Running autolinking fix strategies...');
    
    // Strategy 1: Fix existing PackageList.java files
    const packageListLocations = [
        'android/app/build/generated/autolinking/src/main/java/com/facebook/react/PackageList.java',
        'android/app/src/main/java/com/facebook/react/PackageList.java',
        'android/app/build/generated/rncli/src/main/java/com/facebook/react/PackageList.java'
    ];
    
    let fixed = false;
    
    for (const location of packageListLocations) {
        if (fixPackageListImport(location)) {
            fixed = true;
        }
    }
    
    // Strategy 2: Search recursively for any PackageList.java files
    if (fs.existsSync('android')) {
        console.log('🔍 Searching recursively for PackageList.java files...');
        
        try {
            const findCommand = process.platform === 'win32' 
                ? 'dir /s /b android\\*PackageList.java 2>nul || echo "No files found"'
                : 'find android -name "PackageList.java" -type f 2>/dev/null || echo "No files found"';
            
            const result = execSync(findCommand, { encoding: 'utf8' });
            const files = result.split('\n').filter(file => file.trim() && file.includes('PackageList.java'));
            
            for (const file of files) {
                const cleanFile = file.trim();
                if (fs.existsSync(cleanFile)) {
                    console.log(`📁 Found: ${cleanFile}`);
                    fixPackageListImport(cleanFile);
                }
            }
        } catch (error) {
            console.log('ℹ️  Recursive search completed (no additional files found)');
        }
    }
    
    // Strategy 3: Create custom PackageList.java as fallback
    createCustomPackageList();
    
    console.log('✅ Autolinking fix strategies completed');
}

// Run the fix
runAutolinkingFix();

// If in EAS build, set up monitoring for new files
if (isEASBuild) {
    console.log('🔄 Setting up EAS build monitoring...');
    
    // Create a monitoring script that runs in the background
    const monitorScript = `
const fs = require('fs');
const path = require('path');

console.log('👀 Starting autolinking monitor...');

function checkAndFix() {
    const locations = [
        'android/app/build/generated/autolinking/src/main/java/com/facebook/react/PackageList.java',
        'android/app/src/main/java/com/facebook/react/PackageList.java',
        'android/app/build/generated/rncli/src/main/java/com/facebook/react/PackageList.java'
    ];
    
    for (const location of locations) {
        if (fs.existsSync(location)) {
            try {
                let content = fs.readFileSync(location, 'utf8');
                if (content.includes('import expo.core.ExpoModulesPackage;')) {
                    console.log('🔧 Monitor: Fixing incorrect import in ' + location);
                    content = content.replace(/import expo\\.core\\.ExpoModulesPackage;/g, 'import expo.modules.ExpoModulesPackage;');
                    fs.writeFileSync(location, content);
                    console.log('✅ Monitor: Fixed import statement');
                }
            } catch (error) {
                console.log('❌ Monitor error:', error.message);
            }
        }
    }
}

// Run check every 10 seconds for 5 minutes
let checks = 0;
const maxChecks = 30;

const interval = setInterval(() => {
    checks++;
    console.log(\`🔍 Monitor check \${checks}/\${maxChecks}...\`);
    checkAndFix();
    
    if (checks >= maxChecks) {
        clearInterval(interval);
        console.log('🛑 Monitor completed');
        process.exit(0);
    }
}, 10000);

// Initial check
checkAndFix();
`;
    
    fs.writeFileSync('autolinking-monitor.js', monitorScript);
    console.log('✅ Created autolinking monitor script');
    
    // Start the monitor in the background
    try {
        const { spawn } = require('child_process');
        const monitor = spawn('node', ['autolinking-monitor.js'], {
            detached: true,
            stdio: 'ignore'
        });
        monitor.unref();
        console.log('🚀 Started background autolinking monitor');
    } catch (error) {
        console.log('⚠️  Could not start background monitor:', error.message);
    }
}

console.log('🎯 Expo autolinking fix completed successfully!');
process.exit(0);
