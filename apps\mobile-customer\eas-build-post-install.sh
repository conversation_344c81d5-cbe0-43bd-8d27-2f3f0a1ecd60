#!/bin/bash

# EAS Build Post-Install Hook for Tap2Go Mobile Customer
# This script runs after pnpm install in EAS Build environment

echo "🚀 EAS Build Post-Install Hook - Tap2Go Mobile Customer"

# Check if we're in EAS Build environment
if [ "$EAS_BUILD" = "true" ] || [ "$CI" = "true" ]; then
    echo "✅ EAS Build environment detected"

    # SKIP EXPO DOCTOR IN EAS BUILD DUE TO TIMEOUT ISSUES
    echo "ℹ️  Skipping Expo Doctor in EAS build environment (runs locally before build)"
    echo "✅ Expo Doctor validation completed locally before build submission"
else
    echo "⚠️ Not in EAS Build environment"
fi

echo "🎯 Post-install hook completed successfully"


