#!/bin/bash

# EAS Build Post-Install Hook for Tap2Go Mobile Customer
# This script runs after pnpm install in EAS Build environment

echo "🚀 EAS Build Post-Install Hook - Tap2Go Mobile Customer"

# Check if we're in EAS Build environment
if [ "$EAS_BUILD" = "true" ] || [ "$CI" = "true" ]; then
    echo "✅ EAS Build environment detected"

    # FORCE RUN EXPO DOCTOR TO ENSURE 15/15 SCORE
    echo "🔍 Running Expo Doctor to ensure 15/15 score..."
    if command -v npx >/dev/null 2>&1; then
        npx expo-doctor || echo "⚠️ Expo Doctor failed but continuing build..."
    else
        echo "⚠️ npx not available, skipping expo doctor"
else
    echo "⚠️ Not in EAS Build environment"
fi


