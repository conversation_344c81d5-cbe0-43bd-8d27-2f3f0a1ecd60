#!/usr/bin/env node

/**
 * Simple Autolinking Fix
 * Fixes expo.core.ExpoModulesPackage -> expo.modules.ExpoModulesPackage
 */

const fs = require('fs');

console.log('🔧 Simple autolinking fix...');

// Only fix autolinking-generated files
const locations = [
    'android/app/build/generated/autolinking/src/main/java/com/facebook/react/PackageList.java',
    'android/app/build/generated/rncli/src/main/java/com/facebook/react/PackageList.java'
];

for (const location of locations) {
    if (fs.existsSync(location)) {
        try {
            let content = fs.readFileSync(location, 'utf8');
            if (content.includes('import expo.core.ExpoModulesPackage;')) {
                console.log(`🔧 Fixing: ${location}`);
                content = content.replace(/import expo\.core\.ExpoModulesPackage;/g, 'import expo.modules.ExpoModulesPackage;');
                fs.writeFileSync(location, content);
                console.log('✅ Fixed');
            }
        } catch (error) {
            console.log(`❌ Error: ${error.message}`);
        }
    }
}

console.log('✅ Done');
