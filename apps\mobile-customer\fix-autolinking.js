#!/usr/bin/env node

/**
 * Autolinking Fix - Runs continuously during EAS build
 * Fixes expo.core.ExpoModulesPackage -> expo.modules.ExpoModulesPackage
 */

const fs = require('fs');

console.log('🔧 Autolinking fix starting...');

function fixPackageList(location) {
    if (fs.existsSync(location)) {
        try {
            let content = fs.readFileSync(location, 'utf8');
            if (content.includes('import expo.core.ExpoModulesPackage;')) {
                console.log(`🔧 Fixing: ${location}`);
                content = content.replace(/import expo\.core\.ExpoModulesPackage;/g, 'import expo.modules.ExpoModulesPackage;');
                fs.writeFileSync(location, content);
                console.log('✅ Fixed');
                return true;
            }
        } catch (error) {
            console.log(`❌ Error: ${error.message}`);
        }
    }
    return false;
}

// Locations to check
const locations = [
    'android/app/build/generated/autolinking/src/main/java/com/facebook/react/PackageList.java',
    'android/app/build/generated/rncli/src/main/java/com/facebook/react/PackageList.java'
];

// Run fix immediately
for (const location of locations) {
    fixPackageList(location);
}

// If in EAS build, run continuous monitoring
if (process.env.EAS_BUILD || process.env.CI) {
    console.log('🔄 EAS Build detected - starting continuous monitoring...');

    let checks = 0;
    const maxChecks = 60; // 5 minutes of monitoring

    const interval = setInterval(() => {
        checks++;
        console.log(`🔍 Check ${checks}/${maxChecks}...`);

        let fixed = false;
        for (const location of locations) {
            if (fixPackageList(location)) {
                fixed = true;
            }
        }

        if (fixed) {
            console.log('✅ Found and fixed autolinking issue!');
        }

        if (checks >= maxChecks) {
            clearInterval(interval);
            console.log('🛑 Monitoring completed');
            process.exit(0);
        }
    }, 5000); // Check every 5 seconds

    // Keep process alive
    process.on('SIGTERM', () => {
        clearInterval(interval);
        process.exit(0);
    });
} else {
    console.log('✅ Local environment - fix completed');
}
