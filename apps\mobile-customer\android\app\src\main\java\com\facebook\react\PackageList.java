package com.facebook.react;

import android.app.Application;
import android.content.Context;
import android.content.res.Resources;

import com.facebook.react.ReactPackage;
import com.facebook.react.shell.MainPackageConfig;
import com.facebook.react.shell.MainReactPackage;
import java.util.Arrays;
import java.util.List;

// Expo modules - CORRECT IMPORT PATH
import expo.modules.ExpoModulesPackage;

public class PackageList {
  private Application mApplication;
  private ReactNativeHost mReactNativeHost;
  private MainPackageConfig mConfig;

  public PackageList(ReactNativeHost reactNativeHost) {
    this(reactNativeHost, null);
  }

  public PackageList(Application application) {
    this(application, null);
  }

  public PackageList(ReactNativeHost reactNativeHost, MainPackageConfig config) {
    mReactNativeHost = reactNativeHost;
    mConfig = config;
  }

  public PackageList(Application application, MainPackageConfig config) {
    mReactNativeHost = null;
    mApplication = application;
    mConfig = config;
  }

  private ReactNativeHost getReactNativeHost() {
    return mReactNativeHost;
  }

  private Resources getResources() {
    return getApplication().getResources();
  }

  private Application getApplication() {
    if (mApplication == null) mApplication = getReactNativeHost().getApplication();
    return mApplication;
  }

  private Context getApplicationContext() {
    return getApplication().getApplicationContext();
  }

  public List<ReactPackage> getPackages() {
    return Arrays.<ReactPackage>asList(
      new MainReactPackage(mConfig),
      new ExpoModulesPackage()
    );
  }
}