#!/usr/bin/env node

/**
 * Smart Autolinking Fix - Only Fix Generated Files
 * This script only fixes autolinking-generated files to avoid duplicate class errors
 */

const fs = require('fs');
const path = require('path');

console.log('🧠 Smart autolinking fix starting...');

// Function to fix existing PackageList.java files (not create new ones)
function fixExistingPackageList(filePath) {
    if (!fs.existsSync(filePath)) {
        console.log(`ℹ️  File not found: ${filePath}`);
        return false;
    }
    
    console.log(`🔍 Checking: ${filePath}`);
    
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        
        if (content.includes('import expo.core.ExpoModulesPackage;')) {
            console.log('🔧 Fixing incorrect import: expo.core.ExpoModulesPackage -> expo.modules.ExpoModulesPackage');
            
            // Create backup
            const backupFile = filePath + '.backup';
            fs.writeFileSync(backupFile, content);
            
            // Fix the import statement
            content = content.replace(/import expo\.core\.ExpoModulesPackage;/g, 'import expo.modules.ExpoModulesPackage;');
            
            // Write the fixed content
            fs.writeFileSync(filePath, content);
            
            // Verify the fix
            const fixedContent = fs.readFileSync(filePath, 'utf8');
            if (fixedContent.includes('import expo.modules.ExpoModulesPackage;')) {
                console.log('✅ Successfully fixed import statement');
                return true;
            } else {
                console.log('❌ Failed to fix import statement, restoring backup');
                fs.writeFileSync(filePath, content);
                return false;
            }
        } else {
            console.log('ℹ️  Import already correct or not found');
            return true;
        }
    } catch (error) {
        console.log(`❌ Error processing ${filePath}: ${error.message}`);
        return false;
    }
}

// Only check autolinking-generated locations (not src/main/java to avoid duplicates)
const autolinkingLocations = [
    'android/app/build/generated/autolinking/src/main/java/com/facebook/react/PackageList.java',
    'android/app/build/generated/rncli/src/main/java/com/facebook/react/PackageList.java',
    'android/app/build/generated/react-native-cli/src/main/java/com/facebook/react/PackageList.java'
];

// Fix existing files only
console.log('🔧 Checking and fixing existing autolinking-generated files...');
let fixedAny = false;

for (const location of autolinkingLocations) {
    if (fixExistingPackageList(location)) {
        fixedAny = true;
    }
}

// Search for any other autolinking-generated files
if (fs.existsSync('android/app/build/generated')) {
    console.log('🔍 Searching for other autolinking-generated PackageList.java files...');
    
    try {
        const { execSync } = require('child_process');
        const findCommand = process.platform === 'win32' 
            ? 'dir /s /b android\\app\\build\\generated\\*PackageList.java 2>nul || echo "No files found"'
            : 'find android/app/build/generated -name "PackageList.java" -type f 2>/dev/null || echo "No files found"';
        
        const result = execSync(findCommand, { encoding: 'utf8' });
        const files = result.split('\n').filter(file => file.trim() && file.includes('PackageList.java') && !file.includes('No files found'));
        
        for (const file of files) {
            const cleanFile = file.trim();
            if (fs.existsSync(cleanFile) && !autolinkingLocations.includes(cleanFile.replace(/\\/g, '/'))) {
                console.log(`📁 Found additional file: ${cleanFile}`);
                fixExistingPackageList(cleanFile);
            }
        }
    } catch (error) {
        console.log('ℹ️  Search completed (no additional files found)');
    }
}

// If in EAS build environment, set up monitoring for newly generated files
if (process.env.EAS_BUILD_WORKINGDIR || process.env.EAS_BUILD) {
    console.log('🔄 Setting up monitoring for EAS build...');
    
    // Monitor for newly generated files and fix them
    const monitorInterval = setInterval(() => {
        let foundAndFixed = false;
        
        for (const location of autolinkingLocations) {
            if (fs.existsSync(location)) {
                try {
                    const content = fs.readFileSync(location, 'utf8');
                    if (content.includes('import expo.core.ExpoModulesPackage;')) {
                        console.log(`🔧 Monitor: Found incorrect import in ${location}, fixing...`);
                        fixExistingPackageList(location);
                        foundAndFixed = true;
                    }
                } catch (error) {
                    // Ignore read errors
                }
            }
        }
        
        if (foundAndFixed) {
            console.log('✅ Monitor: Fixed incorrect imports');
        }
    }, 3000); // Check every 3 seconds
    
    // Stop monitoring after 8 minutes
    setTimeout(() => {
        clearInterval(monitorInterval);
        console.log('🛑 Monitoring stopped');
    }, 480000);
    
    console.log('👀 Smart monitoring started (will run for 8 minutes)');
}

console.log('🎯 Smart autolinking fix completed!');

// Exit successfully
process.exit(0);
