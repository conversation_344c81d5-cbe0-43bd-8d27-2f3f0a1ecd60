/**
 * React Native CLI Configuration Override
 * This file overrides the default autolinking behavior to fix the expo.core.ExpoModulesPackage issue
 */

module.exports = {
  project: {
    android: {
      sourceDir: './android',
      appName: 'app',
      packageName: 'com.tap2go.mobile',
    },
  },
  dependencies: {
    'expo-modules-core': {
      platforms: {
        android: {
          sourceDir: '../node_modules/expo-modules-core/android',
          packageImportPath: 'import expo.modules.ExpoModulesPackage;',
          // Override the default package list generation
          packageListPath: 'src/main/java/com/facebook/react/PackageList.java',
        },
      },
    },
    // Ensure expo is properly linked
    'expo': {
      platforms: {
        android: {
          sourceDir: '../node_modules/expo/android',
          packageImportPath: 'import expo.modules.ExpoModulesPackage;',
        },
      },
    },
  },
};
