#!/usr/bin/env node

/**
 * Force Fix Autolinking - Aggressive Approach
 * This script completely replaces any generated PackageList.java with the correct version
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 Force fixing autolinking with aggressive approach...');

// Correct PackageList.java template
const correctPackageListTemplate = `package com.facebook.react;

import android.app.Application;
import android.content.Context;
import android.content.res.Resources;

import com.facebook.react.ReactPackage;
import com.facebook.react.shell.MainPackageConfig;
import com.facebook.react.shell.MainReactPackage;
import java.util.Arrays;
import java.util.List;

// Expo modules - CORRECT IMPORT PATH
import expo.modules.ExpoModulesPackage;

public class PackageList {
  private Application mApplication;
  private ReactNativeHost mReactNativeHost;
  private MainPackageConfig mConfig;

  public PackageList(ReactNativeHost reactNativeHost) {
    this(reactNativeHost, null);
  }

  public PackageList(Application application) {
    this(application, null);
  }

  public PackageList(ReactNativeHost reactNativeHost, MainPackageConfig config) {
    mReactNativeHost = reactNativeHost;
    mConfig = config;
  }

  public PackageList(Application application, MainPackageConfig config) {
    mReactNativeHost = null;
    mApplication = application;
    mConfig = config;
  }

  private ReactNativeHost getReactNativeHost() {
    return mReactNativeHost;
  }

  private Resources getResources() {
    return getApplication().getResources();
  }

  private Application getApplication() {
    if (mApplication == null) mApplication = getReactNativeHost().getApplication();
    return mApplication;
  }

  private Context getApplicationContext() {
    return getApplication().getApplicationContext();
  }

  public List<ReactPackage> getPackages() {
    return Arrays.<ReactPackage>asList(
      new MainReactPackage(mConfig),
      new ExpoModulesPackage()
    );
  }
}`;

// Function to force replace PackageList.java
function forceReplacePackageList(filePath) {
    try {
        // Create directory if it doesn't exist
        const dir = path.dirname(filePath);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
            console.log(`📁 Created directory: ${dir}`);
        }
        
        // Write the correct PackageList.java
        fs.writeFileSync(filePath, correctPackageListTemplate);
        console.log(`✅ Force replaced: ${filePath}`);
        return true;
    } catch (error) {
        console.log(`❌ Failed to replace ${filePath}: ${error.message}`);
        return false;
    }
}

// Only target autolinking-generated locations to avoid duplicate class errors
const packageListLocations = [
    'android/app/build/generated/autolinking/src/main/java/com/facebook/react/PackageList.java',
    'android/app/build/generated/rncli/src/main/java/com/facebook/react/PackageList.java',
    'android/app/build/generated/react-native-cli/src/main/java/com/facebook/react/PackageList.java'
];

// Force replace all possible locations
console.log('🔧 Force replacing all PackageList.java locations...');
for (const location of packageListLocations) {
    forceReplacePackageList(location);
}

// If in EAS build environment, set up continuous monitoring
if (process.env.EAS_BUILD_WORKINGDIR || process.env.EAS_BUILD) {
    console.log('🔄 Setting up continuous monitoring for EAS build...');
    
    // Monitor and replace any newly generated files
    const monitorInterval = setInterval(() => {
        let foundAndFixed = false;
        
        for (const location of packageListLocations) {
            if (fs.existsSync(location)) {
                try {
                    const content = fs.readFileSync(location, 'utf8');
                    if (content.includes('import expo.core.ExpoModulesPackage;')) {
                        console.log(`🔧 Monitor: Found incorrect import in ${location}, force replacing...`);
                        forceReplacePackageList(location);
                        foundAndFixed = true;
                    }
                } catch (error) {
                    // Ignore read errors
                }
            }
        }
        
        if (foundAndFixed) {
            console.log('✅ Monitor: Fixed incorrect imports');
        }
    }, 5000); // Check every 5 seconds
    
    // Stop monitoring after 10 minutes
    setTimeout(() => {
        clearInterval(monitorInterval);
        console.log('🛑 Monitoring stopped');
    }, 600000);
    
    console.log('👀 Continuous monitoring started (will run for 10 minutes)');
}

console.log('🎯 Force autolinking fix completed!');

// Exit successfully
process.exit(0);
